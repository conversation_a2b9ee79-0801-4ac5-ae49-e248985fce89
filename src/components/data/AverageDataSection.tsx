import React, { useState } from 'react';
import { ChevronUp, ChevronDown, Lock, TrendingDown } from 'lucide-react';

interface AverageDataSectionProps {
  rankAndPriceHistory: any;
  isAuthenticated: boolean;
  onLoginClick: () => void;
  isLoading?: boolean;
  country?: string; // Add country prop
}

// Helper function to get currency symbol based on country
const getCurrencySymbol = (country: string): string => {
  switch (country) {
    case 'US': return '$';
    case 'UK': case 'GB': return '£';
    case 'DE': case 'FR': case 'IT': case 'ES': return '€';
    case 'JP': case 'CN': return '¥';
    case 'CA': return 'C$';
    case 'IN': return '₹';
    case 'MX': return '$';
    default: return '£';
  }
};

const AverageDataSection: React.FC<AverageDataSectionProps> = ({ 
  rankAndPriceHistory, 
  isAuthenticated, 
  onLoginClick,
  isLoading = false,
  country = 'GB'
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('current');
  
  const formatCurrency = (value: number | null | undefined) => {
    if (value === undefined || value === null) return `${getCurrencySymbol(country)}0.00`;
    return `${getCurrencySymbol(country)}${value.toFixed(2)}`;
  };
  
  const formatNumber = (value: { toLocaleString: () => any; } | null | undefined) => {
    if (value === undefined || value === null) return 'N/A';
    return value.toLocaleString();
  };
  
  const getPeriodName = (period: string) => {
    switch (period) {
      case 'current': return 'Current';
      case 'avg30': return '30 Day Avg';
      case 'avg90': return '90 Day Avg';
      case 'avg180': return '180 Day Avg';
      case 'avg365': return '365 Day Avg';
      default: return period;
    }
  };
  
  const getPriceTrend = () => {
    if (!rankAndPriceHistory) return { trend: 'stable', percent: 0 };
    
    const current = rankAndPriceHistory.current?.buy_box_price?.price || 0;
    const avg30 = rankAndPriceHistory.avg30?.buy_box_price?.price || 0;
    
    if (current === 0 || avg30 === 0) return { trend: 'stable', percent: 0 };
    
    const diff = ((current - avg30) / avg30) * 100;
    let trend = 'stable';
    
    if (diff > 5) trend = 'up';
    else if (diff < -5) trend = 'down';
    
    return { trend, percent: Math.abs(diff).toFixed(1) };
  };
  
  const getRankImprovement = () => {
    if (!rankAndPriceHistory || !rankAndPriceHistory[selectedPeriod]?.sales_rank_drop) {
      return null;
    }
    
    return rankAndPriceHistory[selectedPeriod].sales_rank_drop;
  };
  
  const priceTrend = getPriceTrend();
  const rankImprovement = getRankImprovement();

  return (
    <div className="border border-black overflow-hidden bg-white shadow mt-2">
      
      <div className="bg-black text-green-500 px-2 py-1  flex items-center justify-center">
        <span className="text-sm font-bold">AVERAGE DATA</span>
      </div>
      
      <div className="p-2">
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-green-500 border-t-transparent"></div>
            <span className="ml-2 text-xs text-gray-600">Loading data...</span>
          </div>
        ) : isAuthenticated && rankAndPriceHistory ? (
          <>
            <div className="flex flex-wrap gap-1 mb-2">
              {['current', 'avg30', 'avg90', 'avg180', 'avg365'].map((period) => (
                <button
                  key={period}
                  onClick={() => setSelectedPeriod(period)}
                  className={`text-xs px-1.5 py-0.5 rounded ${
                    selectedPeriod === period
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 hover:bg-gray-200'
                  }`}
                >
                  {getPeriodName(period)}
                </button>
              ))}
            </div>
            
            <div className="border rounded overflow-hidden">
              <div className="bg-green-50 px-2 py-1 border-b flex justify-between items-center">
                <span className="font-medium text-sm text-green-800">{getPeriodName(selectedPeriod)} Data</span>
                {rankImprovement && (
                  <div className="flex items-center text-xs text-green-700">
                    <TrendingDown size={12} className="mr-0.5" />
                    <span>Rank improved {rankImprovement} positions</span>
                  </div>
                )}
              </div>
              
              <div className="p-2 grid grid-cols-2 gap-2">
                <div className="border rounded bg-gray-50">
                  <div className="bg-gray-100 px-2 py-0.5 border-b text-xs font-medium">Pricing</div>
                  <div className="p-1.5">
                    <table className="w-full text-xs">
                      <tbody>
                        <tr>
                          <td className="py-0.5 text-gray-600">Buy Box:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatCurrency(rankAndPriceHistory[selectedPeriod]?.buy_box_price?.price)}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">New:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatCurrency(rankAndPriceHistory[selectedPeriod]?.new?.price)}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">FBA:</td>
                          <td className="py-0.5 text-right font-medium">
                            {rankAndPriceHistory[selectedPeriod]?.fba ? 
                              formatCurrency(rankAndPriceHistory[selectedPeriod].fba) : 'N/A'}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">FBM:</td>
                          <td className="py-0.5 text-right font-medium">
                            {rankAndPriceHistory[selectedPeriod]?.fbm ? 
                              formatCurrency(rankAndPriceHistory[selectedPeriod].fbm) : 'N/A'}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
                
                <div className="border rounded bg-gray-50">
                  <div className="bg-gray-100 px-2 py-0.5 border-b text-xs font-medium">Metrics</div>
                  <div className="p-1.5">
                    <table className="w-full text-xs">
                      <tbody>
                        <tr>
                          <td className="py-0.5 text-gray-600">Sales Rank:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatNumber(rankAndPriceHistory[selectedPeriod]?.sales_rank)}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">Rating:</td>
                          <td className="py-0.5 text-right font-medium">
                            {rankAndPriceHistory[selectedPeriod]?.rating?.toFixed(1) || 'N/A'}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">Reviews:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatNumber(rankAndPriceHistory[selectedPeriod]?.reviews)}
                          </td>
                        </tr>
                        <tr>
                          <td className="py-0.5 text-gray-600">Offers:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatNumber(rankAndPriceHistory[selectedPeriod]?.offers)}
                          </td>
                        </tr>
                        <tr>  
                          <td className="py-0.5 text-gray-600">Rank Drop:</td>
                          <td className="py-0.5 text-right font-medium">
                            {formatNumber(rankAndPriceHistory[selectedPeriod]?.sales_rank_drop)}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              
              {selectedPeriod === 'current' && (
                <div className={`mt-1 p-1.5 text-xs border-t ${
                  priceTrend.trend === 'up' ? 'bg-red-50 text-red-700' : 
                  priceTrend.trend === 'down' ? 'bg-green-50 text-green-700' : 
                  'bg-blue-50 text-blue-700'
                }`}>
                  <div className="font-medium flex items-center">
                    {priceTrend.trend === 'up' ? (
                      <>
                        <ChevronUp size={14} className="mr-0.5" />
                        Price is {priceTrend.percent}% higher than 30-day average
                      </>
                    ) : priceTrend.trend === 'down' ? (
                      <>
                        <ChevronDown size={14} className="mr-0.5" />
                        Price is {priceTrend.percent}% lower than 30-day average
                      </>
                    ) : (
                      'Price is stable compared to 30-day average'
                    )}
                  </div>
                </div>
              )}
            </div>
          </>
        ) : !isAuthenticated ? (
          // Show login required message when not authenticated
          <div className="flex flex-col items-center justify-center p-4 relative">
            <div className="absolute inset-0 bg-gray-100 blur-sm flex items-center justify-center opacity-50">
              <div className="w-full h-16 bg-gray-200"></div>
            </div>
            <div className="z-10 flex flex-col items-center">
              <Lock className="h-5 w-5 text-gray-500 mb-1" />
              <p className="text-center text-xs text-gray-700">
                Login Required to View Average Data
              </p>
              <button 
                className="mt-1 bg-green-600 text-white px-2 py-0.5 rounded text-xs hover:bg-green-700" 
                onClick={onLoginClick}
              >
                Login
              </button>
            </div>
          </div>
        ) : (
          // Authenticated but no data available
          <div className="p-4 text-center text-xs text-gray-500">
            ClickBuy is updating.
          </div>
        )}
      </div>
    </div>
  );
};

export default AverageDataSection;